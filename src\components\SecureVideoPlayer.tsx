import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Play, AlertTriangle, Loader2 } from 'lucide-react';
import { 
  decodeVideoLinks, 
  parseVideoLinks, 
  generatePlayerNames, 
  isValidVideoLink, 
  extractVideoUrl, 
  isSecureVideoLink 
} from '@/utils/videoSecurity';

interface SecureVideoPlayerProps {
  /** Encoded video links string */
  encodedVideoLinks?: string;
  /** Fallback for legacy videoLinks field */
  legacyVideoLinks?: string;
  /** Title for the player */
  title?: string;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show player selection buttons */
  showPlayerSelection?: boolean;
}

export default function SecureVideoPlayer({
  encodedVideoLinks,
  legacyVideoLinks,
  title = "Video Player",
  className = "",
  showPlayerSelection = true
}: SecureVideoPlayerProps) {
  const [selectedPlayerIndex, setSelectedPlayerIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Decode and parse video links
  const videoData = useMemo(() => {
    try {
      setIsLoading(true);
      setError(null);

      let rawLinks = '';
      
      // Try to decode secure links first, fallback to legacy
      if (encodedVideoLinks) {
        rawLinks = decodeVideoLinks(encodedVideoLinks);
      } else if (legacyVideoLinks) {
        rawLinks = legacyVideoLinks;
        console.warn('Using legacy video links - consider updating to secure format');
      }

      if (!rawLinks) {
        setError('No video links available');
        return { links: [], playerNames: [], currentUrl: '' };
      }

      const links = parseVideoLinks(rawLinks);
      
      if (links.length === 0) {
        setError('No valid video links found');
        return { links: [], playerNames: [], currentUrl: '' };
      }

      // Validate and filter links
      const validLinks = links.filter(link => {
        if (!isValidVideoLink(link)) {
          console.warn('Invalid video link format:', link);
          return false;
        }
        if (!isSecureVideoLink(link)) {
          console.warn('Potentially unsafe video link:', link);
          return false;
        }
        return true;
      });

      if (validLinks.length === 0) {
        setError('No safe video links found');
        return { links: [], playerNames: [], currentUrl: '' };
      }

      const playerNames = generatePlayerNames(validLinks);
      const currentUrl = validLinks[selectedPlayerIndex] ? 
        extractVideoUrl(validLinks[selectedPlayerIndex]) : '';

      return {
        links: validLinks,
        playerNames,
        currentUrl
      };
    } catch (err) {
      console.error('Error processing video links:', err);
      setError('Failed to load video player');
      return { links: [], playerNames: [], currentUrl: '' };
    } finally {
      setIsLoading(false);
    }
  }, [encodedVideoLinks, legacyVideoLinks, selectedPlayerIndex]);

  // Reset selected player when links change
  useEffect(() => {
    if (videoData.links.length > 0 && selectedPlayerIndex >= videoData.links.length) {
      setSelectedPlayerIndex(0);
    }
  }, [videoData.links.length, selectedPlayerIndex]);

  // Loading state
  if (isLoading) {
    return (
      <div className={`bg-background border border-border rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-center space-x-2">
          <Loader2 className="w-5 h-5 animate-spin" />
          <span className="text-muted-foreground">Loading player...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error || videoData.links.length === 0) {
    return (
      <div className={`bg-background border border-border rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-center space-x-2 text-destructive">
          <AlertTriangle className="w-5 h-5" />
          <span>{error || 'No video available'}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-background border border-border rounded-lg overflow-hidden ${className}`}>
      {/* Player Selection */}
      {showPlayerSelection && videoData.links.length > 1 && (
        <div className="p-4 border-b border-border bg-muted/50">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-foreground">Select Player</h3>
            <Badge variant="outline" className="text-xs">
              {videoData.links.length} available
            </Badge>
          </div>
          <div className="flex flex-wrap gap-2">
            {videoData.playerNames.map((name, index) => (
              <Button
                key={index}
                variant={selectedPlayerIndex === index ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedPlayerIndex(index)}
                className="text-xs"
              >
                <Play className="w-3 h-3 mr-1" />
                {name}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Video Player */}
      <div className="relative">
        <div className="aspect-video bg-black">
          {videoData.currentUrl ? (
            <iframe
              src={videoData.currentUrl}
              title={`${title} - ${videoData.playerNames[selectedPlayerIndex] || 'Player'}`}
              className="w-full h-full"
              allowFullScreen
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              sandbox="allow-scripts allow-same-origin allow-presentation"
              onLoad={() => console.log('Video player loaded successfully')}
              onError={() => console.error('Video player failed to load')}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <Play className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>Player not available</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Player Info */}
      {title && (
        <div className="p-3 bg-muted/30">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-foreground">{title}</span>
            {videoData.playerNames[selectedPlayerIndex] && (
              <Badge variant="secondary" className="text-xs">
                {videoData.playerNames[selectedPlayerIndex]}
              </Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
