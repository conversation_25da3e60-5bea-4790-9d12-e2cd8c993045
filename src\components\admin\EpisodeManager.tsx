import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { MediaItem, Episode, Season } from "@/types/media";
import { EpisodeFormData, SeasonFormData } from "@/types/admin";
import { Plus, Edit, Trash2, Play, Calendar, Clock } from "lucide-react";

interface EpisodeManagerProps {
  isOpen: boolean;
  onClose: () => void;
  content: MediaItem | null;
  onSave: (updatedContent: MediaItem) => void;
}

export default function EpisodeManager({ isOpen, onClose, content, onSave }: EpisodeManagerProps) {
  const { toast } = useToast();
  const [selectedSeason, setSelectedSeason] = useState<number>(1);
  const [showAddEpisode, setShowAddEpisode] = useState(false);
  const [showAddSeason, setShowAddSeason] = useState(false);
  const [editingEpisode, setEditingEpisode] = useState<Episode | null>(null);
  const [editingSeason, setEditingSeason] = useState<Season | null>(null);

  const [episodeForm, setEpisodeForm] = useState<EpisodeFormData>({
    title: "",
    season: 1,
    episode: 1,
    description: "",
    videoLink: "",
    runtime: "",
    airDate: "",
    thumbnailUrl: "",
  });

  const [seasonForm, setSeasonForm] = useState<SeasonFormData>({
    seasonNumber: 1,
    title: "",
    description: "",
    posterUrl: "",
  });

  if (!content || content.type !== "series") return null;

  const seasons = content.seasons || [];
  const currentSeason = seasons.find(s => s.seasonNumber === selectedSeason);
  const episodes = currentSeason?.episodes || [];

  const resetEpisodeForm = () => {
    const nextEpisodeNumber = episodes.length + 1;
    setEpisodeForm({
      title: "",
      season: selectedSeason,
      episode: nextEpisodeNumber,
      description: "",
      videoLink: "",
      runtime: "",
      airDate: "",
      thumbnailUrl: "",
    });
  };

  const resetSeasonForm = () => {
    const nextSeasonNumber = Math.max(...seasons.map(s => s.seasonNumber), 0) + 1;
    setSeasonForm({
      seasonNumber: nextSeasonNumber,
      title: "",
      description: "",
      posterUrl: "",
    });
  };

  const handleAddEpisode = () => {
    if (!episodeForm.title || !episodeForm.videoLink) {
      toast({
        title: "Error",
        description: "Please fill in episode title and video link",
        variant: "destructive",
      });
      return;
    }

    const newEpisode: Episode = {
      id: Date.now().toString(),
      title: episodeForm.title,
      season: episodeForm.season,
      episode: episodeForm.episode,
      description: episodeForm.description,
      videoLink: episodeForm.videoLink,
      runtime: episodeForm.runtime,
      airDate: episodeForm.airDate,
      thumbnailUrl: episodeForm.thumbnailUrl,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const updatedSeasons = [...seasons];
    const seasonIndex = updatedSeasons.findIndex(s => s.seasonNumber === selectedSeason);
    
    if (seasonIndex >= 0) {
      updatedSeasons[seasonIndex] = {
        ...updatedSeasons[seasonIndex],
        episodes: [...updatedSeasons[seasonIndex].episodes, newEpisode],
        updatedAt: new Date().toISOString(),
      };
    } else {
      // Create new season if it doesn't exist
      const newSeason: Season = {
        id: Date.now().toString(),
        seasonNumber: selectedSeason,
        episodes: [newEpisode],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      updatedSeasons.push(newSeason);
    }

    const updatedContent: MediaItem = {
      ...content,
      seasons: updatedSeasons,
      totalSeasons: updatedSeasons.length,
      totalEpisodes: updatedSeasons.reduce((total, season) => total + season.episodes.length, 0),
      updatedAt: new Date().toISOString(),
    };

    onSave(updatedContent);
    setShowAddEpisode(false);
    resetEpisodeForm();
    
    toast({
      title: "Episode added",
      description: `Episode ${newEpisode.episode} added to Season ${selectedSeason}`,
    });
  };

  const handleAddSeason = () => {
    if (!seasonForm.seasonNumber) {
      toast({
        title: "Error",
        description: "Please specify season number",
        variant: "destructive",
      });
      return;
    }

    const newSeason: Season = {
      id: Date.now().toString(),
      seasonNumber: seasonForm.seasonNumber,
      title: seasonForm.title,
      description: seasonForm.description,
      posterUrl: seasonForm.posterUrl,
      episodes: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const updatedSeasons = [...seasons, newSeason].sort((a, b) => a.seasonNumber - b.seasonNumber);

    const updatedContent: MediaItem = {
      ...content,
      seasons: updatedSeasons,
      totalSeasons: updatedSeasons.length,
      updatedAt: new Date().toISOString(),
    };

    onSave(updatedContent);
    setShowAddSeason(false);
    setSelectedSeason(seasonForm.seasonNumber);
    resetSeasonForm();
    
    toast({
      title: "Season added",
      description: `Season ${newSeason.seasonNumber} added successfully`,
    });
  };

  const handleDeleteEpisode = (episodeId: string) => {
    const episode = episodes.find(e => e.id === episodeId);
    if (!episode) return;

    if (window.confirm(`Are you sure you want to delete "${episode.title}"?`)) {
      const updatedSeasons = seasons.map(season => {
        if (season.seasonNumber === selectedSeason) {
          return {
            ...season,
            episodes: season.episodes.filter(e => e.id !== episodeId),
            updatedAt: new Date().toISOString(),
          };
        }
        return season;
      });

      const updatedContent: MediaItem = {
        ...content,
        seasons: updatedSeasons,
        totalEpisodes: updatedSeasons.reduce((total, season) => total + season.episodes.length, 0),
        updatedAt: new Date().toISOString(),
      };

      onSave(updatedContent);
      
      toast({
        title: "Episode deleted",
        description: `"${episode.title}" has been deleted`,
      });
    }
  };

  const handleDeleteSeason = (seasonNumber: number) => {
    const season = seasons.find(s => s.seasonNumber === seasonNumber);
    if (!season) return;

    if (window.confirm(`Are you sure you want to delete Season ${seasonNumber} and all its episodes?`)) {
      const updatedSeasons = seasons.filter(s => s.seasonNumber !== seasonNumber);

      const updatedContent: MediaItem = {
        ...content,
        seasons: updatedSeasons,
        totalSeasons: updatedSeasons.length,
        totalEpisodes: updatedSeasons.reduce((total, season) => total + season.episodes.length, 0),
        updatedAt: new Date().toISOString(),
      };

      onSave(updatedContent);
      
      // Switch to first available season or season 1
      if (updatedSeasons.length > 0) {
        setSelectedSeason(updatedSeasons[0].seasonNumber);
      } else {
        setSelectedSeason(1);
      }
      
      toast({
        title: "Season deleted",
        description: `Season ${seasonNumber} and all its episodes have been deleted`,
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Manage Episodes: {content.title}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Season Selection and Management */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Label>Season:</Label>
              <div className="flex gap-2">
                {seasons.map(season => (
                  <Button
                    key={season.seasonNumber}
                    variant={selectedSeason === season.seasonNumber ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedSeason(season.seasonNumber)}
                  >
                    Season {season.seasonNumber}
                    <Badge variant="secondary" className="ml-2">
                      {season.episodes.length}
                    </Badge>
                  </Button>
                ))}
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  resetSeasonForm();
                  setShowAddSeason(true);
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Season
              </Button>
              {currentSeason && (
                <Button
                  variant="outline"
                  size="sm"
                  className="text-destructive hover:text-destructive"
                  onClick={() => handleDeleteSeason(selectedSeason)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Season
                </Button>
              )}
            </div>
          </div>

          {/* Episodes List */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                Season {selectedSeason} Episodes ({episodes.length})
              </h3>
              <Button
                onClick={() => {
                  resetEpisodeForm();
                  setShowAddEpisode(true);
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Episode
              </Button>
            </div>

            {episodes.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No episodes in this season. Add your first episode!
              </div>
            ) : (
              <div className="grid gap-4">
                {episodes
                  .sort((a, b) => a.episode - b.episode)
                  .map(episode => (
                    <div
                      key={episode.id}
                      className="border border-border rounded-lg p-4 space-y-2"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">
                              S{episode.season}E{episode.episode}
                            </Badge>
                            <h4 className="font-semibold">{episode.title}</h4>
                          </div>
                          {episode.description && (
                            <p className="text-sm text-muted-foreground mt-1">
                              {episode.description}
                            </p>
                          )}
                          <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                            {episode.runtime && (
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {episode.runtime} min
                              </div>
                            )}
                            {episode.airDate && (
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {new Date(episode.airDate).toLocaleDateString()}
                              </div>
                            )}
                            {episode.videoLink && (
                              <div className="flex items-center gap-1">
                                <Play className="h-3 w-3" />
                                Video Available
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingEpisode(episode)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-destructive hover:text-destructive"
                            onClick={() => handleDeleteEpisode(episode.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>

          {/* Close Button */}
          <div className="flex justify-end pt-4 border-t">
            <Button onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>

      {/* Add Episode Dialog */}
      <Dialog open={showAddEpisode} onOpenChange={setShowAddEpisode}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add Episode to Season {selectedSeason}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>Episode Title *</Label>
                <Input
                  value={episodeForm.title}
                  onChange={(e) => setEpisodeForm(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Episode title"
                />
              </div>
              <div>
                <Label>Season</Label>
                <Input
                  type="number"
                  value={episodeForm.season}
                  onChange={(e) => setEpisodeForm(prev => ({ ...prev, season: parseInt(e.target.value) || 1 }))}
                  min="1"
                />
              </div>
              <div>
                <Label>Episode Number</Label>
                <Input
                  type="number"
                  value={episodeForm.episode}
                  onChange={(e) => setEpisodeForm(prev => ({ ...prev, episode: parseInt(e.target.value) || 1 }))}
                  min="1"
                />
              </div>
            </div>

            <div>
              <Label>Description</Label>
              <Textarea
                value={episodeForm.description}
                onChange={(e) => setEpisodeForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Episode description"
                rows={3}
              />
            </div>

            <div>
              <Label>Video Link / Embed Code *</Label>
              <Textarea
                value={episodeForm.videoLink}
                onChange={(e) => setEpisodeForm(prev => ({ ...prev, videoLink: e.target.value }))}
                placeholder="Enter streaming link or embed code"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>Runtime (minutes)</Label>
                <Input
                  value={episodeForm.runtime}
                  onChange={(e) => setEpisodeForm(prev => ({ ...prev, runtime: e.target.value }))}
                  placeholder="45"
                />
              </div>
              <div>
                <Label>Air Date</Label>
                <Input
                  type="date"
                  value={episodeForm.airDate}
                  onChange={(e) => setEpisodeForm(prev => ({ ...prev, airDate: e.target.value }))}
                />
              </div>
              <div>
                <Label>Thumbnail URL</Label>
                <Input
                  value={episodeForm.thumbnailUrl}
                  onChange={(e) => setEpisodeForm(prev => ({ ...prev, thumbnailUrl: e.target.value }))}
                  placeholder="https://example.com/thumb.jpg"
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setShowAddEpisode(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddEpisode}>
                Add Episode
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Add Season Dialog */}
      <Dialog open={showAddSeason} onOpenChange={setShowAddSeason}>
        <DialogContent className="max-w-xl">
          <DialogHeader>
            <DialogTitle>Add New Season</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label>Season Number *</Label>
              <Input
                type="number"
                value={seasonForm.seasonNumber}
                onChange={(e) => setSeasonForm(prev => ({ ...prev, seasonNumber: parseInt(e.target.value) || 1 }))}
                min="1"
                placeholder="1"
              />
            </div>

            <div>
              <Label>Season Title (optional)</Label>
              <Input
                value={seasonForm.title}
                onChange={(e) => setSeasonForm(prev => ({ ...prev, title: e.target.value }))}
                placeholder="e.g., The Beginning"
              />
            </div>

            <div>
              <Label>Season Description (optional)</Label>
              <Textarea
                value={seasonForm.description}
                onChange={(e) => setSeasonForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Season description"
                rows={3}
              />
            </div>

            <div>
              <Label>Season Poster URL (optional)</Label>
              <Input
                value={seasonForm.posterUrl}
                onChange={(e) => setSeasonForm(prev => ({ ...prev, posterUrl: e.target.value }))}
                placeholder="https://example.com/season-poster.jpg"
              />
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setShowAddSeason(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddSeason}>
                Add Season
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}
