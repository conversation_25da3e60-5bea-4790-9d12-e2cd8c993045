
import * as React from "react";
import CardGrid from "./CardGrid";
import { MediaItem } from "@/types/media";

interface Props {
  items: MediaItem[];
  pageSize?: number;
}

export default function PaginatedGrid({ items, pageSize = 8 }: Props) {
  const [page, setPage] = React.useState(1);

  const pageCount = Math.ceil(items.length / pageSize);

  const pageItems = items.slice((page - 1) * pageSize, page * pageSize);

  return (
    <div className="w-full">
      <CardGrid items={pageItems} />

      {pageCount > 1 && (
        <div className="flex justify-center items-center space-x-1.5 mt-7">
          <button
            className="px-2 py-1 rounded-md bg-secondary hover:bg-secondary/80 font-semibold text-secondary-foreground text-xs disabled:opacity-50"
            onClick={() => setPage(page - 1)}
            disabled={page === 1}
          >
            Prev
          </button>
          {Array.from({ length: pageCount }, (_, i) => (
            <button
              key={i}
              className={`px-2 py-1 rounded-md font-semibold text-xs ${
                page === i + 1 ? "bg-primary text-primary-foreground" : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
              }`}
              onClick={() => setPage(i + 1)}
            >
              {i + 1}
            </button>
          ))}
          <button
            className="px-2 py-1 rounded-md bg-secondary hover:bg-secondary/80 font-semibold text-secondary-foreground text-xs disabled:opacity-50"
            onClick={() => setPage(page + 1)}
            disabled={page === pageCount}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}
