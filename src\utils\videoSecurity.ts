/**
 * Video Link Security Utilities
 * 
 * This module provides client-side security for video embed links to prevent
 * easy extraction via browser developer tools. While not foolproof against
 * determined attackers, it significantly raises the barrier for casual users.
 * 
 * Future Enhancement: Replace with server-side proxy when backend is implemented.
 */

// Simple encoding key - in production, this should be environment-specific
const ENCODING_KEY = 'StreamDB_2024_Security_Key_v1';

/**
 * Simple XOR-based encoding for video links
 * This is obfuscation, not true encryption, but prevents casual inspection
 */
function xorEncode(text: string, key: string): string {
  let result = '';
  for (let i = 0; i < text.length; i++) {
    const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
    result += String.fromCharCode(charCode);
  }
  return result;
}

/**
 * Base64 encode with URL-safe characters
 */
function base64UrlEncode(str: string): string {
  return btoa(str)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * Base64 decode with URL-safe characters
 */
function base64UrlDecode(str: string): string {
  // Add padding if needed
  const padding = '='.repeat((4 - str.length % 4) % 4);
  const base64 = str.replace(/-/g, '+').replace(/_/g, '/') + padding;
  return atob(base64);
}

/**
 * Encode video links for secure storage
 * @param videoLinks - Raw video links (one per line)
 * @returns Encoded string safe for client-side storage
 */
export function encodeVideoLinks(videoLinks: string): string {
  if (!videoLinks || videoLinks.trim() === '') {
    return '';
  }

  try {
    // Add timestamp and random salt for additional obfuscation
    const timestamp = Date.now().toString();
    const salt = Math.random().toString(36).substring(2, 15);
    const payload = JSON.stringify({
      links: videoLinks,
      timestamp,
      salt
    });

    // XOR encode the payload
    const encoded = xorEncode(payload, ENCODING_KEY);
    
    // Base64 encode for safe storage
    return base64UrlEncode(encoded);
  } catch (error) {
    console.error('Error encoding video links:', error);
    return '';
  }
}

/**
 * Decode video links for playback
 * @param encodedLinks - Encoded video links string
 * @returns Decoded video links or empty string if invalid
 */
export function decodeVideoLinks(encodedLinks: string): string {
  if (!encodedLinks || encodedLinks.trim() === '') {
    return '';
  }

  try {
    // Base64 decode
    const decoded = base64UrlDecode(encodedLinks);
    
    // XOR decode
    const payload = xorEncode(decoded, ENCODING_KEY);
    
    // Parse JSON
    const data = JSON.parse(payload);
    
    // Validate structure
    if (data && typeof data.links === 'string') {
      return data.links;
    }
    
    return '';
  } catch (error) {
    console.error('Error decoding video links:', error);
    return '';
  }
}

/**
 * Parse video links into an array of individual links
 * @param videoLinks - Raw or decoded video links string
 * @returns Array of individual video links
 */
export function parseVideoLinks(videoLinks: string): string[] {
  if (!videoLinks || videoLinks.trim() === '') {
    return [];
  }

  return videoLinks
    .split('\n')
    .map(link => link.trim())
    .filter(link => link.length > 0);
}

/**
 * Generate player names for multiple video links
 * @param links - Array of video links
 * @returns Array of player names (Player 1, Player 2, etc.)
 */
export function generatePlayerNames(links: string[]): string[] {
  return links.map((_, index) => `Player ${index + 1}`);
}

/**
 * Validate if a string appears to be a valid video embed link
 * @param link - Video link to validate
 * @returns True if link appears valid
 */
export function isValidVideoLink(link: string): boolean {
  if (!link || typeof link !== 'string') {
    return false;
  }

  const trimmedLink = link.trim();
  
  // Check for common video embed patterns
  const patterns = [
    /^https?:\/\/.*\/embed\//i,
    /^\/\/.*\/embed\//i,
    /^<iframe.*src=["'].*["'].*<\/iframe>/i,
    /youtube\.com\/embed\//i,
    /vimeo\.com\/video\//i,
    /dailymotion\.com\/embed\//i
  ];

  return patterns.some(pattern => pattern.test(trimmedLink));
}

/**
 * Extract iframe src from embed code if it's an iframe
 * @param embedCode - Embed code (could be iframe or direct URL)
 * @returns Clean URL or original code
 */
export function extractVideoUrl(embedCode: string): string {
  if (!embedCode) return '';

  const trimmed = embedCode.trim();
  
  // If it's an iframe, extract the src
  const iframeMatch = trimmed.match(/src=["']([^"']+)["']/i);
  if (iframeMatch) {
    return iframeMatch[1];
  }

  // If it's already a URL, return as is
  return trimmed;
}

/**
 * Security check: Prevent common XSS patterns in video links
 * @param link - Video link to check
 * @returns True if link appears safe
 */
export function isSecureVideoLink(link: string): boolean {
  if (!link) return false;

  const dangerous = [
    'javascript:',
    'data:',
    'vbscript:',
    '<script',
    'onload=',
    'onerror=',
    'onclick='
  ];

  const lowerLink = link.toLowerCase();
  return !dangerous.some(pattern => lowerLink.includes(pattern));
}
